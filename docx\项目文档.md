# 智慧渔场监控系统 - 项目文档

## 1. 项目概述

### 项目名称
智慧渔场监控系统 (Smart Fish Farm Monitoring System)

### 项目描述
基于Android平台的智慧渔场监控系统，集成环境监测、设备控制、电力管理、报警处理等功能，为渔场提供全方位的智能化管理解决方案。

### 技术栈
- **开发语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Repository Pattern
- **依赖注入**: Hilt
- **数据库**: Room
- **网络通信**: Retrofit + OkHttp
- **实时通信**: MQTT (Eclipse Paho)
- **异步处理**: Kotlin Coroutines + Flow

## 2. 架构设计

### 整体架构
```
┌─────────────────┐
│   UI Layer      │ ← Jetpack Compose + ViewModels
├─────────────────┤
│ Business Layer  │ ← Repository + Use Cases
├─────────────────┤
│   Data Layer    │ ← Room Database + Retrofit + MQTT
└─────────────────┘
```

### 核心组件
1. **UI层**: Compose界面 + ViewModel
2. **业务层**: Repository接口与实现
3. **数据层**: 本地数据库 + 网络API + 实时数据流

## 3. 功能模块

### 3.1 仪表盘模块 (Dashboard)
- 系统状态总览
- 设备在线状态
- 环境数据概览
- 电力系统状态
- 最近报警信息

### 3.2 环境监测模块 (Environment)
- 实时环境数据显示
- 历史数据趋势分析
- 环境阈值设置
- 数据统计与分析

### 3.3 设备管理模块 (Devices)
- 设备状态监控
- 远程设备控制
- 运行模式切换
- 设备参数配置

### 3.4 电力系统模块 (Power System)
- 太阳能发电监控
- 电池状态管理
- 负载功耗分析
- 电力系统统计

### 3.5 报警管理模块 (Alerts)
- 报警信息展示
- 报警级别分类
- 报警确认与处理
- 报警历史记录

### 3.6 系统设置模块 (Settings)
- 网络配置
- 通知设置
- 用户信息管理
- 数据导入导出

## 4. 数据模型

### 4.1 核心实体
- **EnvironmentData**: 环境数据
- **DeviceStatus**: 设备状态
- **PowerSystemData**: 电力系统数据
- **AlertRecord**: 报警记录
- **ControlCommand**: 控制指令
- **DeviceConfig**: 设备配置

### 4.2 枚举类型
- **AlertLevel**: 报警级别
- **AlertType**: 报警类型
- **AlertStatus**: 报警状态
- **OperationMode**: 运行模式
- **BatteryStatus**: 电池状态
- **CommandType**: 指令类型
- **CommandStatus**: 指令状态
- **Priority**: 优先级

## 5. 实现进度

### 已完成功能
- ✅ 数据模型定义（Entity、DTO、枚举类型）
- ✅ 数据库层实现（Room数据库、DAO接口、TypeConverter）
- ✅ 网络层实现（Retrofit API接口、数据传输对象）
- ✅ MQTT实时通信服务
- ✅ UI界面设计（所有主要界面的Compose实现）
- ✅ ViewModel层实现（6个ViewModel类，完整的业务逻辑）
- ✅ 依赖注入配置（Hilt模块配置，Repository接口与实现分离）
- ✅ UI与数据层连接（MainActivity集成ViewModel，真实数据显示）
- ✅ 模拟数据服务（MockDataService和DataInitializationService实现）
- ✅ 功能测试与优化（编译错误修复，项目可正常构建）

### 项目状态
🎉 **项目已完成所有核心功能实现，可正常构建和运行！**

### 最新更新 (2025-01-30)
- 修复了重复的AlertLevel枚举定义冲突
- 修复了MockDataService中DeviceConfig和EnvironmentThreshold的参数名称问题
- 修复了DataInitializationService中的类型转换问题（Double to Float, Date to Long）
- 修复了MainActivity中when表达式缺少CRITICAL分支的问题
- 修复了null值不能赋给非空类型的问题
- 项目现在可以成功构建，无编译错误

### 最新更新 (2025-01-30 下午)
- **修复底部导航栏选中状态显示问题**：修复了BottomNavigationBar中所有NavigationBarItem的selected属性都被硬编码为false的问题，现在能正确显示当前选中的页面
- **修复设备ID不一致问题**：统一了所有ViewModel中的设备ID，从"fishfarm_device_001"改为"FISH_FARM_001"，与MockDataService和DataInitializationService保持一致
- **修复ViewModel数据收集阻塞问题**：发现并修复了ViewModel中initializeData方法的严重逻辑错误
  - **问题**：多个collect调用在同一个协程中会相互阻塞，导致页面一直显示"加载中"
  - **解决方案**：将每个数据流的collect调用分别放在独立的协程中，避免相互阻塞
  - **影响**：这是导致页面无法加载数据的根本原因
- **解决页面加载问题**：修复了底部按键"总览"、"环境"、"设备"三个按钮点击后加载不出页面的根本原因
- **新增配置指南文档**：创建了完整的配置和故障排除指南
  - **智慧渔场APP配置指南.md**：详细的系统配置说明，包括网络、MQTT、设备、阈值等各项配置
  - **快速配置向导.md**：5分钟快速上手指南，提供标准配置模板和常见问题解决方案
  - **设备连接故障排除指南.md**：专业的故障诊断和排除流程，包含网络、MQTT、硬件等各类问题
- **实现真正的MQTT连接检测功能**：解决了用户反映的连接测试问题
  - **问题**：设置页面中的连接按钮只是简单切换状态，没有真正的连接检测机制
  - **解决方案**：
    - **MqttService.kt**：新增`testConnection()`方法，实现真正的MQTT连接测试，支持自定义broker地址、用户名密码
    - **DeviceRepositoryImpl.kt**：新增`testMqttConnection()`、`connectMqtt()`、`disconnectMqtt()`等方法，提供完整的MQTT连接管理
    - **SettingsViewModel.kt**：新增`testMqttConnection()`、`connectMqtt()`、`disconnectMqtt()`等方法，管理连接状态和UI反馈
    - **MainActivity.kt**：重构设置页面UI，集成ViewModel的连接功能，添加实时状态显示、加载指示器、错误处理
  - **功能特性**：
    - 真正的MQTT连接测试，支持超时检测（15秒）
    - 实时连接状态显示，包括连接中、已连接、未连接状态
    - 完整的错误处理和用户反馈
    - 支持自定义MQTT服务器地址、用户名、密码
    - 连接测试使用独立的临时客户端，不影响正常连接
- 涉及的文件：
  - MainActivity.kt：添加了currentBackStackEntryAsState导入，修复了导航选中状态；重构设置页面UI，集成真正的连接功能
  - DashboardViewModel.kt：统一设备ID为"FISH_FARM_001"，修复数据收集逻辑
  - EnvironmentViewModel.kt：统一设备ID为"FISH_FARM_001"，修复数据收集逻辑
  - DevicesViewModel.kt：统一设备ID为"FISH_FARM_001"，修复数据收集逻辑
  - PowerSystemViewModel.kt：统一设备ID为"FISH_FARM_001"，修复数据收集逻辑
  - AlertsViewModel.kt：统一设备ID为"FISH_FARM_001"，修复数据收集逻辑
  - MqttService.kt：新增真正的MQTT连接测试功能
  - DeviceRepositoryImpl.kt：新增MQTT连接管理方法
  - SettingsViewModel.kt：新增MQTT连接状态管理和UI交互方法
- **修复连接功能相互影响问题**：解决了MQTT连接和PLC连接相互影响的问题
  - **问题**：PLCConnectionCard和MQTTSettingsCard共享相同的ViewModel状态和方法，导致点击一个连接按钮会影响另一个
  - **解决方案**：
    - **SettingsViewModel.kt**：扩展UI状态，添加独立的PLC和MQTT状态字段（`isTestingPLC`、`isTestingMQTT`、`plcMessage`、`mqttMessage`等）
    - **SettingsViewModel.kt**：新增独立的PLC连接管理方法（`testPlcConnection()`、`connectPlc()`、`disconnectPlc()`、`getPlcConnectionStatus()`）
    - **MainActivity.kt**：修改PLCConnectionCard使用PLC相关的方法和状态，MQTTSettingsCard使用MQTT相关的方法和状态
  - **功能特性**：
    - PLC连接和MQTT连接完全独立，互不影响
    - 各自独立的连接状态显示和错误处理
    - 各自独立的测试和连接功能
    - 独立的加载状态指示器
- **修复PLC连接检测机制**：解决了PLC连接测试不准确的问题
  - **问题**：之前的PLC连接测试只检查IP地址格式和端口范围，任何格式正确的IP都会显示"连接成功"
  - **解决方案**：
    - **SettingsViewModel.kt**：实现真正的网络连接测试，使用Socket进行实际的网络连通性检测
    - **testNetworkConnection()**：新增私有方法，执行真实的Socket连接测试，设置5秒连接超时和10秒总超时
    - **testPlcConnection()**：修改为使用真实网络连接测试，而不是简单的格式验证
    - **connectPlc()**：修改为使用真实网络连接测试来验证连接状态
    - **MainActivity.kt**：在UI中添加提示信息"将进行真实网络连接测试"
  - **功能特性**：
    - 真正的Socket网络连接测试
    - 合理的超时设置（连接超时5秒，总超时10秒）
    - 在IO线程中执行网络操作，不阻塞UI
    - 正确的资源管理和异常处理
    - 只有真正能连接的IP地址和端口才会显示连接成功

## 6. 技术实现细节

### 6.1 依赖注入架构
使用Hilt进行依赖注入管理：
- **DatabaseModule**: 提供Room数据库和DAO实例
- **NetworkModule**: 提供Retrofit、OkHttp、ApiService实例
- **RepositoryModule**: 提供Repository实现类
- **FishFarmApplication**: 应用程序入口，启用Hilt

### 6.2 ViewModel实现
每个功能模块对应一个ViewModel：
- **DashboardViewModel**: 仪表盘数据管理
- **EnvironmentViewModel**: 环境数据管理
- **DevicesViewModel**: 设备控制管理
- **PowerSystemViewModel**: 电力系统管理
- **AlertsViewModel**: 报警管理
- **SettingsViewModel**: 设置管理

### 6.3 数据流架构
- 使用StateFlow管理UI状态
- 使用SharedFlow处理事件
- Repository层整合本地数据库、网络API和MQTT实时数据
- 通过Flow实现响应式数据流

### 6.4 UI集成
- MainActivity使用@AndroidEntryPoint启用Hilt
- 各Screen函数通过hiltViewModel()获取ViewModel实例
- 使用collectAsStateWithLifecycle()收集状态数据
- 实现加载状态、错误处理和真实数据显示

## 7. 下一步计划

1. **模拟数据服务实现**
   - 创建MockDataService生成测试数据
   - 实现数据初始化和定时更新
   - 模拟MQTT消息推送

2. **功能测试与优化**
   - 单元测试编写
   - UI测试验证
   - 性能优化
   - 错误处理完善

3. **部署与发布**
   - APK构建配置
   - 版本管理
   - 文档完善

## 8. 开发环境

- **IDE**: Android Studio
- **Kotlin版本**: 1.9.0+
- **Android SDK**: API 24-36
- **Gradle版本**: 8.0+
- **依赖管理**: Version Catalog

## 9. 项目结构

```
app/src/main/java/com/ys/myapplication/
├── MainActivity.kt                    # 主Activity
├── FishFarmApplication.kt            # 应用程序类
├── data/
│   ├── database/                     # 数据库层
│   ├── model/                        # 数据模型
│   ├── network/                      # 网络层
│   ├── realtime/                     # 实时通信
│   └── repository/                   # 仓库层
├── di/                              # 依赖注入
├── ui/
│   ├── theme/                       # 主题配置
│   └── viewmodel/                   # ViewModel层
└── utils/                           # 工具类
```

## 10. 更新日志

### v1.0.0 (当前版本)
- 完成基础架构搭建
- 实现所有核心功能模块
- 完成UI与数据层连接
- 集成依赖注入框架

---

*文档最后更新时间: 2025-01-30*
