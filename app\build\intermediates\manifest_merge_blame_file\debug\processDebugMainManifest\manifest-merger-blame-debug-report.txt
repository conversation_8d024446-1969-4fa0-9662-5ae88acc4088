1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ys.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\MyApplication\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\MyApplication\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\MyApplication\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\MyApplication\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->D:\MyApplication\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\MyApplication\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 通知权限 -->
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->D:\MyApplication\app\src\main\AndroidManifest.xml:11:5-77
17-->D:\MyApplication\app\src\main\AndroidManifest.xml:11:22-74
18
19    <!-- 唤醒设备权限（用于MQTT保持连接） -->
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->D:\MyApplication\app\src\main\AndroidManifest.xml:14:5-68
20-->D:\MyApplication\app\src\main\AndroidManifest.xml:14:22-65
21
22    <permission
22-->[androidx.core:core:1.13.1] D:\Android-gradle\caches\8.13\transforms\4e70e8e8f09a3cf55a9c56f85d7ed2d6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
23        android:name="com.ys.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.13.1] D:\Android-gradle\caches\8.13\transforms\4e70e8e8f09a3cf55a9c56f85d7ed2d6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.13.1] D:\Android-gradle\caches\8.13\transforms\4e70e8e8f09a3cf55a9c56f85d7ed2d6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.ys.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.13.1] D:\Android-gradle\caches\8.13\transforms\4e70e8e8f09a3cf55a9c56f85d7ed2d6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.13.1] D:\Android-gradle\caches\8.13\transforms\4e70e8e8f09a3cf55a9c56f85d7ed2d6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
27
28    <application
28-->D:\MyApplication\app\src\main\AndroidManifest.xml:16:5-41:19
29        android:name="com.ys.myapplication.FishFarmApplication"
29-->D:\MyApplication\app\src\main\AndroidManifest.xml:17:9-44
30        android:allowBackup="true"
30-->D:\MyApplication\app\src\main\AndroidManifest.xml:18:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.13.1] D:\Android-gradle\caches\8.13\transforms\4e70e8e8f09a3cf55a9c56f85d7ed2d6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->D:\MyApplication\app\src\main\AndroidManifest.xml:19:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->D:\MyApplication\app\src\main\AndroidManifest.xml:20:9-54
36        android:icon="@mipmap/ic_launcher"
36-->D:\MyApplication\app\src\main\AndroidManifest.xml:21:9-43
37        android:label="@string/app_name"
37-->D:\MyApplication\app\src\main\AndroidManifest.xml:22:9-41
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->D:\MyApplication\app\src\main\AndroidManifest.xml:23:9-54
39        android:supportsRtl="true"
39-->D:\MyApplication\app\src\main\AndroidManifest.xml:24:9-35
40        android:testOnly="true"
41        android:theme="@style/Theme.MyApplication" >
41-->D:\MyApplication\app\src\main\AndroidManifest.xml:25:9-51
42
43        <!-- MQTT 服务 -->
44        <service android:name="org.eclipse.paho.android.service.MqttService" />
44-->D:\MyApplication\app\src\main\AndroidManifest.xml:29:9-80
44-->D:\MyApplication\app\src\main\AndroidManifest.xml:29:18-77
45
46        <activity
46-->D:\MyApplication\app\src\main\AndroidManifest.xml:31:9-40:20
47            android:name="com.ys.myapplication.MainActivity"
47-->D:\MyApplication\app\src\main\AndroidManifest.xml:32:13-41
48            android:exported="true"
48-->D:\MyApplication\app\src\main\AndroidManifest.xml:33:13-36
49            android:label="@string/app_name"
49-->D:\MyApplication\app\src\main\AndroidManifest.xml:34:13-45
50            android:theme="@style/Theme.MyApplication" >
50-->D:\MyApplication\app\src\main\AndroidManifest.xml:35:13-55
51            <intent-filter>
51-->D:\MyApplication\app\src\main\AndroidManifest.xml:36:13-39:29
52                <action android:name="android.intent.action.MAIN" />
52-->D:\MyApplication\app\src\main\AndroidManifest.xml:37:17-69
52-->D:\MyApplication\app\src\main\AndroidManifest.xml:37:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->D:\MyApplication\app\src\main\AndroidManifest.xml:38:17-77
54-->D:\MyApplication\app\src\main\AndroidManifest.xml:38:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->[androidx.compose.ui:ui-test-manifest:1.7.2] D:\Android-gradle\caches\8.13\transforms\8755606327b64207c49ec16ce5b0e84b\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
58            android:name="androidx.activity.ComponentActivity"
58-->[androidx.compose.ui:ui-test-manifest:1.7.2] D:\Android-gradle\caches\8.13\transforms\8755606327b64207c49ec16ce5b0e84b\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
59            android:exported="true" />
59-->[androidx.compose.ui:ui-test-manifest:1.7.2] D:\Android-gradle\caches\8.13\transforms\8755606327b64207c49ec16ce5b0e84b\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
60        <activity
60-->[androidx.compose.ui:ui-tooling-android:1.7.2] D:\Android-gradle\caches\8.13\transforms\66c0ae953debba71cfd8afd05c5b62cf\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
61            android:name="androidx.compose.ui.tooling.PreviewActivity"
61-->[androidx.compose.ui:ui-tooling-android:1.7.2] D:\Android-gradle\caches\8.13\transforms\66c0ae953debba71cfd8afd05c5b62cf\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
62            android:exported="true" />
62-->[androidx.compose.ui:ui-tooling-android:1.7.2] D:\Android-gradle\caches\8.13\transforms\66c0ae953debba71cfd8afd05c5b62cf\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
63
64        <provider
64-->[androidx.emoji2:emoji2:1.3.0] D:\Android-gradle\caches\8.13\transforms\d21817dc63783b3e2eca28bba365f9ce\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
65            android:name="androidx.startup.InitializationProvider"
65-->[androidx.emoji2:emoji2:1.3.0] D:\Android-gradle\caches\8.13\transforms\d21817dc63783b3e2eca28bba365f9ce\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
66            android:authorities="com.ys.myapplication.androidx-startup"
66-->[androidx.emoji2:emoji2:1.3.0] D:\Android-gradle\caches\8.13\transforms\d21817dc63783b3e2eca28bba365f9ce\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
67            android:exported="false" >
67-->[androidx.emoji2:emoji2:1.3.0] D:\Android-gradle\caches\8.13\transforms\d21817dc63783b3e2eca28bba365f9ce\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
68            <meta-data
68-->[androidx.emoji2:emoji2:1.3.0] D:\Android-gradle\caches\8.13\transforms\d21817dc63783b3e2eca28bba365f9ce\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.emoji2.text.EmojiCompatInitializer"
69-->[androidx.emoji2:emoji2:1.3.0] D:\Android-gradle\caches\8.13\transforms\d21817dc63783b3e2eca28bba365f9ce\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
70                android:value="androidx.startup" />
70-->[androidx.emoji2:emoji2:1.3.0] D:\Android-gradle\caches\8.13\transforms\d21817dc63783b3e2eca28bba365f9ce\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.lifecycle:lifecycle-process:2.8.7] D:\Android-gradle\caches\8.13\transforms\bb81130f583b8e99bb7427bb5d5120e9\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
72-->[androidx.lifecycle:lifecycle-process:2.8.7] D:\Android-gradle\caches\8.13\transforms\bb81130f583b8e99bb7427bb5d5120e9\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
73                android:value="androidx.startup" />
73-->[androidx.lifecycle:lifecycle-process:2.8.7] D:\Android-gradle\caches\8.13\transforms\bb81130f583b8e99bb7427bb5d5120e9\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
76                android:value="androidx.startup" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
77        </provider>
78
79        <service
79-->[androidx.room:room-runtime:2.6.1] D:\Android-gradle\caches\8.13\transforms\5cf7d6ea428f11eda42b4856778a9535\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
80            android:name="androidx.room.MultiInstanceInvalidationService"
80-->[androidx.room:room-runtime:2.6.1] D:\Android-gradle\caches\8.13\transforms\5cf7d6ea428f11eda42b4856778a9535\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
81            android:directBootAware="true"
81-->[androidx.room:room-runtime:2.6.1] D:\Android-gradle\caches\8.13\transforms\5cf7d6ea428f11eda42b4856778a9535\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
82            android:exported="false" />
82-->[androidx.room:room-runtime:2.6.1] D:\Android-gradle\caches\8.13\transforms\5cf7d6ea428f11eda42b4856778a9535\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
83
84        <receiver
84-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
85            android:name="androidx.profileinstaller.ProfileInstallReceiver"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
86            android:directBootAware="false"
86-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
87            android:enabled="true"
87-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
88            android:exported="true"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
89            android:permission="android.permission.DUMP" >
89-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
91                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
91-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
94                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
94-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
97                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
97-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
100                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
100-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\Android-gradle\caches\8.13\transforms\0369a0cb9c05b70837df03cb39f73a21\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
101            </intent-filter>
102        </receiver>
103    </application>
104
105</manifest>
