/ Header Record For PersistentHashMapValueStorage android.app.Application$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase android.os.Parcelable kotlin.Enum kotlin.Enum android.os.Parcelable kotlin.Enum kotlin.Enum kotlin.Enum android.os.Parcelable kotlin.Enum android.os.Parcelable android.os.Parcelable kotlin.Enum android.os.Parcelable android.os.Parcelable kotlin.Enum android.os.Parcelable6 5com.ys.myapplication.data.repository.DeviceRepository androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel