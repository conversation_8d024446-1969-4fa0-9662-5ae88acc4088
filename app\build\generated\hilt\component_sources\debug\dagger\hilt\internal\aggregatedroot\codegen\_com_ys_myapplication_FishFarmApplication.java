package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.ys.myapplication.FishFarmApplication",
    rootPackage = "com.ys.myapplication",
    originatingRoot = "com.ys.myapplication.FishFarmApplication",
    originatingRootPackage = "com.ys.myapplication",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "FishFarmApplication",
    originatingRootSimpleNames = "FishFarmApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_ys_myapplication_FishFarmApplication {
}
