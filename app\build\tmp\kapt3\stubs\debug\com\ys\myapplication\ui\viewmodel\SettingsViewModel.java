package com.ys.myapplication.ui.viewmodel;

/**
 * 设置界面ViewModel
 * 管理系统设置、用户偏好和配置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u001c\u001a\u00020\u001dJ\u0006\u0010\u001e\u001a\u00020\u001dJ\u0006\u0010\u001f\u001a\u00020\u001dJ\u001e\u0010 \u001a\u00020\u001d2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\"2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\"J\u001e\u0010$\u001a\u00020\u001d2\u0006\u0010%\u001a\u00020\"2\u0006\u0010&\u001a\u00020\"2\u0006\u0010\'\u001a\u00020\"J\u0006\u0010(\u001a\u00020\u001dJ\u0006\u0010)\u001a\u00020\u001dJ\u0006\u0010*\u001a\u00020\u001dJ\u0006\u0010+\u001a\u00020,J\u0006\u0010-\u001a\u00020,J\u000e\u0010.\u001a\u00020\u001d2\u0006\u0010/\u001a\u00020\"J\b\u00100\u001a\u00020\u001dH\u0002J\u0006\u00101\u001a\u00020\u001dJ&\u00102\u001a\u00020\u001d2\u0006\u00103\u001a\u00020\"2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\"2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\"J\u0006\u00104\u001a\u00020\u001dJ\u001e\u00104\u001a\u00020,2\u0006\u0010%\u001a\u00020\"2\u0006\u0010&\u001a\u00020\"H\u0082@\u00a2\u0006\u0002\u00105J\u0016\u00106\u001a\u00020\u001d2\u0006\u0010%\u001a\u00020\"2\u0006\u0010&\u001a\u00020\"J\u000e\u00107\u001a\u00020\u001d2\u0006\u00108\u001a\u00020\u0007J\u000e\u00109\u001a\u00020\u001d2\u0006\u00108\u001a\u00020\tJ\u000e\u0010:\u001a\u00020\u001d2\u0006\u00108\u001a\u00020\u000bJ\u000e\u0010;\u001a\u00020\u001d2\u0006\u0010\u001a\u001a\u00020\u000fR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\r0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0013\u00a8\u0006<"}, d2 = {"Lcom/ys/myapplication/ui/viewmodel/SettingsViewModel;", "Landroidx/lifecycle/ViewModel;", "deviceRepository", "Lcom/ys/myapplication/data/repository/DeviceRepository;", "(Lcom/ys/myapplication/data/repository/DeviceRepository;)V", "_networkSettings", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/ys/myapplication/ui/viewmodel/NetworkSettings;", "_notificationSettings", "Lcom/ys/myapplication/ui/viewmodel/NotificationSettings;", "_systemSettings", "Lcom/ys/myapplication/ui/viewmodel/SystemSettings;", "_uiState", "Lcom/ys/myapplication/ui/viewmodel/SettingsUiState;", "_userInfo", "Lcom/ys/myapplication/ui/viewmodel/UserInfo;", "networkSettings", "Lkotlinx/coroutines/flow/StateFlow;", "getNetworkSettings", "()Lkotlinx/coroutines/flow/StateFlow;", "notificationSettings", "getNotificationSettings", "systemSettings", "getSystemSettings", "uiState", "getUiState", "userInfo", "getUserInfo", "clearCache", "", "clearError", "clearMessage", "connectMqtt", "username", "", "password", "connectPlc", "ipAddress", "port", "deviceId", "disconnectMqtt", "disconnectPlc", "exportData", "getMqttConnectionStatus", "", "getPlcConnectionStatus", "importData", "filePath", "loadSettings", "resetSettings", "testMqttConnection", "brokerUrl", "testNetworkConnection", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "testPlcConnection", "updateNetworkSettings", "settings", "updateNotificationSettings", "updateSystemSettings", "updateUserInfo", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SettingsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.ys.myapplication.data.repository.DeviceRepository deviceRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.ys.myapplication.ui.viewmodel.SettingsUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.SettingsUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.ys.myapplication.ui.viewmodel.SystemSettings> _systemSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.SystemSettings> systemSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.ys.myapplication.ui.viewmodel.NotificationSettings> _notificationSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.NotificationSettings> notificationSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.ys.myapplication.ui.viewmodel.NetworkSettings> _networkSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.NetworkSettings> networkSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.ys.myapplication.ui.viewmodel.UserInfo> _userInfo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.UserInfo> userInfo = null;
    
    @javax.inject.Inject()
    public SettingsViewModel(@org.jetbrains.annotations.NotNull()
    com.ys.myapplication.data.repository.DeviceRepository deviceRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.SettingsUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.SystemSettings> getSystemSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.NotificationSettings> getNotificationSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.NetworkSettings> getNetworkSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.ys.myapplication.ui.viewmodel.UserInfo> getUserInfo() {
        return null;
    }
    
    /**
     * 加载设置
     */
    private final void loadSettings() {
    }
    
    /**
     * 更新系统设置
     */
    public final void updateSystemSettings(@org.jetbrains.annotations.NotNull()
    com.ys.myapplication.ui.viewmodel.SystemSettings settings) {
    }
    
    /**
     * 更新通知设置
     */
    public final void updateNotificationSettings(@org.jetbrains.annotations.NotNull()
    com.ys.myapplication.ui.viewmodel.NotificationSettings settings) {
    }
    
    /**
     * 更新网络设置
     */
    public final void updateNetworkSettings(@org.jetbrains.annotations.NotNull()
    com.ys.myapplication.ui.viewmodel.NetworkSettings settings) {
    }
    
    /**
     * 更新用户信息
     */
    public final void updateUserInfo(@org.jetbrains.annotations.NotNull()
    com.ys.myapplication.ui.viewmodel.UserInfo userInfo) {
    }
    
    /**
     * 测试网络连接
     */
    public final void testNetworkConnection() {
    }
    
    /**
     * 测试MQTT连接
     */
    public final void testMqttConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String brokerUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String username, @org.jetbrains.annotations.Nullable()
    java.lang.String password) {
    }
    
    /**
     * 测试PLC连接
     */
    public final void testPlcConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, @org.jetbrains.annotations.NotNull()
    java.lang.String port) {
    }
    
    /**
     * 测试网络连接
     */
    private final java.lang.Object testNetworkConnection(java.lang.String ipAddress, java.lang.String port, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * 连接MQTT服务器
     */
    public final void connectMqtt(@org.jetbrains.annotations.Nullable()
    java.lang.String username, @org.jetbrains.annotations.Nullable()
    java.lang.String password) {
    }
    
    /**
     * 连接PLC设备
     */
    public final void connectPlc(@org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, @org.jetbrains.annotations.NotNull()
    java.lang.String port, @org.jetbrains.annotations.NotNull()
    java.lang.String deviceId) {
    }
    
    /**
     * 断开MQTT连接
     */
    public final void disconnectMqtt() {
    }
    
    /**
     * 断开PLC连接
     */
    public final void disconnectPlc() {
    }
    
    /**
     * 获取MQTT连接状态
     */
    public final boolean getMqttConnectionStatus() {
        return false;
    }
    
    /**
     * 获取PLC连接状态
     */
    public final boolean getPlcConnectionStatus() {
        return false;
    }
    
    /**
     * 导出数据
     */
    public final void exportData() {
    }
    
    /**
     * 导入数据
     */
    public final void importData(@org.jetbrains.annotations.NotNull()
    java.lang.String filePath) {
    }
    
    /**
     * 清除缓存
     */
    public final void clearCache() {
    }
    
    /**
     * 重置设置
     */
    public final void resetSettings() {
    }
    
    /**
     * 清除消息
     */
    public final void clearMessage() {
    }
    
    /**
     * 清除错误
     */
    public final void clearError() {
    }
}