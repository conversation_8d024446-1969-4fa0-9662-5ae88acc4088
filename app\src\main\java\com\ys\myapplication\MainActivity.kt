package com.ys.myapplication

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.ys.myapplication.ui.theme.MyApplicationTheme
import com.ys.myapplication.ui.viewmodel.*
import com.ys.myapplication.data.model.*
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.*

// 自定义颜色定义
private val Orange = Color(0xFFFF9800)
private val Purple = Color(0xFF9C27B0)

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            MyApplicationTheme {
                FishFarmApp()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FishFarmApp() {
    val navController = rememberNavController()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        "智慧渔场监控系统",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    ) 
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        },
        bottomBar = {
            BottomNavigationBar(navController)
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "dashboard",
            modifier = Modifier.padding(innerPadding)
        ) {
            composable("dashboard") { DashboardScreen() }
            composable("environment") { EnvironmentScreen() }
            composable("devices") { DevicesScreen() }
            composable("power") { PowerSystemScreen() }
            composable("alerts") { AlertsScreen() }
            composable("settings") { SettingsScreen() }
        }
    }
}

@Composable
fun BottomNavigationBar(navController: NavHostController) {
    val items = listOf(
        BottomNavItem("dashboard", "总览", Icons.Filled.Dashboard),
        BottomNavItem("environment", "环境", Icons.Filled.Thermostat),
        BottomNavItem("devices", "设备", Icons.Filled.Settings),
        BottomNavItem("power", "电力", Icons.Filled.ElectricBolt),
        BottomNavItem("alerts", "报警", Icons.Filled.Warning),
        BottomNavItem("settings", "设置", Icons.Filled.SettingsApplications)
    )

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    NavigationBar {
        items.forEach { item ->
            NavigationBarItem(
                icon = { Icon(item.icon, contentDescription = item.label) },
                label = { Text(item.label) },
                selected = currentRoute == item.route,
                onClick = {
                    navController.navigate(item.route) {
                        popUpTo(navController.graph.startDestinationId)
                        launchSingleTop = true
                    }
                }
            )
        }
    }
}

data class BottomNavItem(
    val route: String,
    val label: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
)

@Composable
fun DashboardScreen(
    viewModel: DashboardViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val environmentData by viewModel.environmentData.collectAsStateWithLifecycle()
    val deviceStatus by viewModel.deviceStatus.collectAsStateWithLifecycle()
    val powerSystemData by viewModel.powerSystemData.collectAsStateWithLifecycle()
    val recentAlerts by viewModel.recentAlerts.collectAsStateWithLifecycle()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                "系统总览",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        if (uiState.isLoading) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        } else {
            item {
                QuickStatusCard(
                    systemStatusSummary = viewModel.getSystemStatusSummary()
                )
            }
            item {
                EnvironmentOverviewCard(
                    environmentData = environmentData
                )
            }
            item {
                PowerSystemOverviewCard(
                    powerSystemData = powerSystemData
                )
            }
            item {
                RecentAlertsCard(
                    alerts = recentAlerts
                )
            }
        }

        uiState.error?.let { error ->
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
                ) {
                    Text(
                        text = "错误: $error",
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuickStatusCard(systemStatusSummary: SystemStatusSummary) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "设备状态",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatusIndicator(
                    "在线设备",
                    "${systemStatusSummary.onlineDevices}/${systemStatusSummary.totalDevices}",
                    if (systemStatusSummary.onlineDevices == systemStatusSummary.totalDevices) Color.Green else Color.Red
                )
                StatusIndicator(
                    "运行模式",
                    systemStatusSummary.operationMode,
                    Color.Blue
                )
                StatusIndicator(
                    "系统状态",
                    if (systemStatusSummary.systemHealthy) "正常" else "异常",
                    if (systemStatusSummary.systemHealthy) Color.Green else Color.Red
                )
            }
        }
    }
}

@Composable
fun StatusIndicator(label: String, value: String, color: Color) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        Text(
            value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnvironmentOverviewCard(environmentData: EnvironmentData?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "环境监测",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            if (environmentData != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    EnvironmentMetric(
                        "温度",
                        "${environmentData.temperature}°C",
                        Icons.Filled.Thermostat,
                        when (environmentData.temperatureStatus) {
                            AlertStatus.NORMAL -> Color.Green
                            AlertStatus.HIGH_ALERT -> Color.Yellow
                            AlertStatus.LOW_ALERT -> Color.Blue
                            AlertStatus.CRITICAL -> Color.Red
                        }
                    )
                    EnvironmentMetric(
                        "湿度",
                        "${environmentData.humidity}%",
                        Icons.Filled.Water,
                        when (environmentData.humidityStatus) {
                            AlertStatus.NORMAL -> Color.Blue
                            AlertStatus.HIGH_ALERT -> Color.Yellow
                            AlertStatus.LOW_ALERT -> Color.Cyan
                            AlertStatus.CRITICAL -> Color.Red
                        }
                    )
                    EnvironmentMetric(
                        "光照",
                        "${environmentData.lightIntensity.toInt()}Lx",
                        Icons.Filled.LightMode,
                        Color.Yellow
                    )
                }
            } else {
                Text(
                    "暂无环境数据",
                    modifier = Modifier.padding(16.dp),
                    color = Color.Gray
                )
            }
        }
    }
}

@Composable
fun EnvironmentMetric(
    label: String, 
    value: String, 
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            icon,
            contentDescription = label,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        Text(
            value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PowerSystemOverviewCard(powerSystemData: PowerSystemData?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "电力系统",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            if (powerSystemData != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    PowerMetric(
                        "太阳能",
                        "${powerSystemData.solarPower.toInt()}W",
                        Color.Green
                    )
                    PowerMetric(
                        "电池",
                        "${powerSystemData.batteryLevel.toInt()}%",
                        when {
                            powerSystemData.batteryLevel > 50 -> Color.Green
                            powerSystemData.batteryLevel > 20 -> Color.Yellow
                            else -> Color.Red
                        }
                    )
                    PowerMetric(
                        "负载",
                        "${powerSystemData.loadPower.toInt()}W",
                        Color.Blue
                    )
                }
            } else {
                Text(
                    "暂无电力数据",
                    modifier = Modifier.padding(16.dp),
                    color = Color.Gray
                )
            }
        }
    }
}

@Composable
fun PowerMetric(label: String, value: String, color: Color) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        Text(
            value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecentAlertsCard(alerts: List<AlertRecord>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "最近报警",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            if (alerts.isNotEmpty()) {
                alerts.take(3).forEach { alert ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Filled.Warning,
                            contentDescription = "警告",
                            tint = when (alert.alertLevel) {
                                AlertLevel.INFO -> Color.Blue
                                AlertLevel.WARNING -> Color.Yellow
                                AlertLevel.CRITICAL -> Color.Red
                                AlertLevel.EMERGENCY -> Color.Magenta
                            },
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text(
                                alert.alertMessage,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(alert.timestamp),
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            } else {
                Text(
                    "暂无报警信息",
                    modifier = Modifier.padding(16.dp),
                    color = Color.Gray
                )
            }
        }
    }
}

// 环境监测界面
@Composable
fun EnvironmentScreen(
    viewModel: EnvironmentViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val currentEnvironmentData by viewModel.currentEnvironmentData.collectAsStateWithLifecycle()
    val historicalData by viewModel.historicalData.collectAsStateWithLifecycle()
    val thresholds by viewModel.thresholds.collectAsStateWithLifecycle()
    val statistics by viewModel.statistics.collectAsStateWithLifecycle()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                "环境监测",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        if (uiState.isLoading) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        } else {
            item {
                CurrentEnvironmentCard(
                    environmentData = currentEnvironmentData
                )
            }
            item {
                TemperatureTrendCard()
            }
            item {
                HumidityTrendCard()
            }
            item {
                LightIntensityCard()
            }
            item {
                EnvironmentThresholdsCard(
                    thresholds = thresholds,
                    onUpdateThreshold = { threshold ->
                        viewModel.updateThreshold(threshold)
                    }
                )
            }
        }

        uiState.error?.let { error ->
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
                ) {
                    Text(
                        text = "错误: $error",
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CurrentEnvironmentCard(environmentData: EnvironmentData?) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "当前环境状态",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            if (environmentData != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    EnvironmentMetricCard(
                        "温度",
                        "${environmentData.temperature}°C",
                        Icons.Filled.Thermostat,
                        Color.Red,
                        when (environmentData.temperatureStatus) {
                            AlertStatus.NORMAL -> "正常"
                            AlertStatus.HIGH_ALERT -> "高温警告"
                            AlertStatus.LOW_ALERT -> "低温警告"
                            AlertStatus.CRITICAL -> "危险"
                        },
                        when (environmentData.temperatureStatus) {
                            AlertStatus.NORMAL -> Color.Green
                            AlertStatus.HIGH_ALERT -> Color.Yellow
                            AlertStatus.LOW_ALERT -> Color.Blue
                            AlertStatus.CRITICAL -> Color.Red
                        }
                    )
                    EnvironmentMetricCard(
                        "湿度",
                        "${environmentData.humidity}%",
                        Icons.Filled.Water,
                        Color.Blue,
                        when (environmentData.humidityStatus) {
                            AlertStatus.NORMAL -> "正常"
                            AlertStatus.HIGH_ALERT -> "高湿警告"
                            AlertStatus.LOW_ALERT -> "低湿警告"
                            AlertStatus.CRITICAL -> "危险"
                        },
                        when (environmentData.humidityStatus) {
                            AlertStatus.NORMAL -> Color.Green
                            AlertStatus.HIGH_ALERT -> Color.Yellow
                            AlertStatus.LOW_ALERT -> Color.Cyan
                            AlertStatus.CRITICAL -> Color.Red
                        }
                    )
                    EnvironmentMetricCard(
                        "光照",
                        "${environmentData.lightIntensity.toInt()}Lx",
                        Icons.Filled.LightMode,
                        Color.Yellow,
                        "正常",
                        Color.Green
                    )
                }
            } else {
                Text(
                    "暂无环境数据",
                    modifier = Modifier.padding(16.dp),
                    color = Color.Gray
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                EnvironmentMetricCard(
                    "气压",
                    "1013hPa",
                    Icons.Filled.Speed,
                    Color.Gray,
                    "正常",
                    Color.Green
                )
                EnvironmentMetricCard(
                    "CO2",
                    "450ppm",
                    Icons.Filled.Air,
                    Color.Green,
                    "正常",
                    Color.Green
                )
                EnvironmentMetricCard(
                    "风速",
                    "2.5m/s",
                    Icons.Filled.Air,
                    Color.Cyan,
                    "正常",
                    Color.Green
                )
            }
        }
    }
}

@Composable
fun EnvironmentMetricCard(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    iconColor: Color,
    status: String,
    statusColor: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(8.dp)
    ) {
        Icon(
            icon,
            contentDescription = label,
            tint = iconColor,
            modifier = Modifier.size(32.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        Text(
            value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            status,
            fontSize = 10.sp,
            color = statusColor,
            fontWeight = FontWeight.Medium
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemperatureTrendCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "温度趋势",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = { /* TODO: 24小时 */ },
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("24h", fontSize = 12.sp)
                    }
                    OutlinedButton(
                        onClick = { /* TODO: 7天 */ },
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("7天", fontSize = 12.sp)
                    }
                    OutlinedButton(
                        onClick = { /* TODO: 30天 */ },
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("30天", fontSize = 12.sp)
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 模拟温度数据点
            val temperatureData = listOf(22.5f, 23.1f, 24.2f, 25.3f, 24.8f, 23.9f, 22.7f)
            val hours = listOf("00:00", "04:00", "08:00", "12:00", "16:00", "20:00", "24:00")
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Bottom
            ) {
                temperatureData.forEachIndexed { index, temp ->
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier
                                .width(20.dp)
                                .height(((temp - 20) * 8).dp)
                                .background(
                                    color = when {
                                        temp < 20 -> Color.Blue
                                        temp > 30 -> Color.Red
                                        else -> Color.Green
                                    },
                                    shape = RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp)
                                )
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            temp.toString(),
                            fontSize = 10.sp,
                            color = Color.Gray
                        )
                        if (index < hours.size) {
                            Text(
                                hours[index],
                                fontSize = 8.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("最高: 25.3°C", fontSize = 12.sp, color = Color.Red)
                Text("最低: 22.5°C", fontSize = 12.sp, color = Color.Blue)
                Text("平均: 23.8°C", fontSize = 12.sp, color = Color.Green)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HumidityTrendCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "湿度趋势",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = { /* TODO: 24小时 */ },
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("24h", fontSize = 12.sp)
                    }
                    OutlinedButton(
                        onClick = { /* TODO: 7天 */ },
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("7天", fontSize = 12.sp)
                    }
                    OutlinedButton(
                        onClick = { /* TODO: 30天 */ },
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("30天", fontSize = 12.sp)
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 模拟湿度数据点
            val humidityData = listOf(65f, 68f, 72f, 75f, 73f, 70f, 68f)
            val hours = listOf("00:00", "04:00", "08:00", "12:00", "16:00", "20:00", "24:00")
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Bottom
            ) {
                humidityData.forEachIndexed { index, humidity ->
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier
                                .width(20.dp)
                                .height((humidity * 0.8).dp)
                                .background(
                                    color = when {
                                        humidity < 40 -> Color.Red
                                        humidity > 80 -> Color.Blue
                                        else -> Color.Green
                                    },
                                    shape = RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp)
                                )
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            humidity.toInt().toString(),
                            fontSize = 10.sp,
                            color = Color.Gray
                        )
                        if (index < hours.size) {
                            Text(
                                hours[index],
                                fontSize = 8.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("最高: 75%", fontSize = 12.sp, color = Color.Blue)
                Text("最低: 65%", fontSize = 12.sp, color = Color.Red)
                Text("平均: 70%", fontSize = 12.sp, color = Color.Green)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LightIntensityCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "光照强度监测",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text("当前光照", fontSize = 14.sp, color = Color.Gray)
                    Text("15000 Lux", fontSize = 24.sp, fontWeight = FontWeight.Bold, color = Color.Yellow)
                }
                
                Column {
                    Text("太阳高度", fontSize = 14.sp, color = Color.Gray)
                    Text("45°", fontSize = 24.sp, fontWeight = FontWeight.Bold, color = Orange)
                }
                
                Column {
                    Text("云层覆盖", fontSize = 14.sp, color = Color.Gray)
                    Text("30%", fontSize = 24.sp, fontWeight = FontWeight.Bold, color = Color.Gray)
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 光照强度条
            LinearProgressIndicator(
                progress = 0.75f,
                modifier = Modifier.fillMaxWidth(),
                color = Color.Yellow,
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("0 Lux", fontSize = 12.sp, color = Color.Gray)
                Text("20000 Lux", fontSize = 12.sp, color = Color.Gray)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("日累计: 8.5小时", fontSize = 12.sp, color = Color.Green)
                Text("强度等级: 强", fontSize = 12.sp, color = Color.Yellow)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EnvironmentThresholdsCard(
    thresholds: EnvironmentThreshold?,
    onUpdateThreshold: (EnvironmentThreshold) -> Unit
) {
    var tempMin by remember { mutableStateOf(thresholds?.temperatureMin?.toString() ?: "20") }
    var tempMax by remember { mutableStateOf(thresholds?.temperatureMax?.toString() ?: "30") }
    var humidityMin by remember { mutableStateOf("40") }
    var humidityMax by remember { mutableStateOf("80") }
    var lightMin by remember { mutableStateOf("5000") }
    var lightMax by remember { mutableStateOf("20000") }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "环境阈值设置",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 温度阈值
            Text("温度阈值 (°C)", fontSize = 14.sp, fontWeight = FontWeight.Medium)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = tempMin,
                    onValueChange = { tempMin = it },
                    label = { Text("最小值") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
                OutlinedTextField(
                    value = tempMax,
                    onValueChange = { tempMax = it },
                    label = { Text("最大值") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 湿度阈值
            Text("湿度阈值 (%)", fontSize = 14.sp, fontWeight = FontWeight.Medium)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = humidityMin,
                    onValueChange = { humidityMin = it },
                    label = { Text("最小值") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
                OutlinedTextField(
                    value = humidityMax,
                    onValueChange = { humidityMax = it },
                    label = { Text("最大值") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 光照阈值
            Text("光照阈值 (Lux)", fontSize = 14.sp, fontWeight = FontWeight.Medium)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = lightMin,
                    onValueChange = { lightMin = it },
                    label = { Text("最小值") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
                OutlinedTextField(
                    value = lightMax,
                    onValueChange = { lightMax = it },
                    label = { Text("最大值") },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { /* TODO: 保存阈值设置 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("保存设置")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 恢复默认值 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("恢复默认")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AirQualityCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "空气质量监测",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                AirQualityMetric("PM2.5", "15", "μg/m³", Color.Green)
                AirQualityMetric("PM10", "35", "μg/m³", Color.Green)
                AirQualityMetric("CO2", "450", "ppm", Color.Green)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                AirQualityMetric("TVOC", "0.2", "mg/m³", Color.Green)
                AirQualityMetric("甲醛", "0.05", "mg/m³", Color.Green)
                AirQualityMetric("AQI", "45", "", Color.Green)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("空气质量等级", fontSize = 14.sp, fontWeight = FontWeight.Medium)
                Text("优", fontSize = 16.sp, fontWeight = FontWeight.Bold, color = Color.Green)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = 0.45f,
                modifier = Modifier.fillMaxWidth(),
                color = Color.Green,
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("0", fontSize = 12.sp, color = Color.Gray)
                Text("100", fontSize = 12.sp, color = Color.Gray)
            }
        }
    }
}

@Composable
fun AirQualityMetric(
    label: String,
    value: String,
    unit: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        Text(
            value,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            unit,
            fontSize = 10.sp,
            color = Color.Gray
        )
    }
}

@Composable
fun DevicesScreen(
    viewModel: DevicesViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val deviceStatus by viewModel.deviceStatus.collectAsStateWithLifecycle()
    val operationMode by viewModel.operationMode.collectAsStateWithLifecycle()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                "设备管理",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        if (uiState.isLoading) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        } else {
            item {
                DeviceStatusOverviewCard(
                    deviceStatus = deviceStatus,
                    operationMode = operationMode,
                    onModeChange = { mode ->
                        viewModel.changeOperationMode(mode)
                    }
                )
            }
            item {
                VentilationSystemCard(
                    deviceStatus = deviceStatus,
                    onControlCommand = { command ->
                        viewModel.sendControlCommand(command)
                    }
                )
            }
            item {
                WaterPumpCard(
                    deviceStatus = deviceStatus,
                    onControlCommand = { command ->
                        viewModel.sendControlCommand(command)
                    }
                )
            }
            item {
                DryingSystemCard(
                    deviceStatus = deviceStatus,
                    onControlCommand = { command ->
                        viewModel.sendControlCommand(command)
                    }
                )
            }
            item {
                LightingSystemCard(
                    deviceStatus = deviceStatus,
                    onControlCommand = { command ->
                        viewModel.sendControlCommand(command)
                    }
                )
            }
            item {
                TemperatureControlCard(
                    deviceStatus = deviceStatus,
                    onControlCommand = { command ->
                        viewModel.sendControlCommand(command)
                    }
                )
            }
        }

        uiState.error?.let { error ->
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
                ) {
                    Text(
                        text = "错误: $error",
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceStatusOverviewCard(
    deviceStatus: DeviceStatus?,
    operationMode: OperationMode,
    onModeChange: (OperationMode) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "设备状态总览",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                DeviceStatusItem("运行中", 4, Color.Green)
                DeviceStatusItem("待机", 2, Color.Yellow)
                DeviceStatusItem("故障", 1, Color.Red)
                DeviceStatusItem("离线", 0, Color.Gray)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { /* TODO: 全部启动 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("全部启动")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 全部停止 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("全部停止")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 刷新状态 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("刷新状态")
                }
            }
        }
    }
}

@Composable
fun DeviceStatusItem(label: String, count: Int, color: Color) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            count.toString(),
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VentilationSystemCard(
    deviceStatus: DeviceStatus?,
    onControlCommand: (CommandType) -> Unit
) {
    var isRunning by remember { mutableStateOf(deviceStatus?.fanStatus ?: true) }
    var fanSpeed by remember { mutableStateOf(deviceStatus?.fanSpeed?.toFloat() ?: 75f) }
    var autoMode by remember { mutableStateOf(deviceStatus?.operationMode == OperationMode.AUTO) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Filled.Air,
                        contentDescription = "通风系统",
                        tint = if (isRunning) Color.Green else Color.Gray
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        "通风系统",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (isRunning) Icons.Filled.PlayArrow else Icons.Filled.Stop,
                        contentDescription = if (isRunning) "运行中" else "已停止",
                        tint = if (isRunning) Color.Green else Color.Red,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        if (isRunning) "运行中" else "已停止",
                        fontSize = 12.sp,
                        color = if (isRunning) Color.Green else Color.Red
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("风扇转速: ${fanSpeed.toInt()}%")
                Text("功率: ${(fanSpeed * 1.2).toInt()}W", color = Color.Blue)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Slider(
                value = fanSpeed,
                onValueChange = { fanSpeed = it },
                valueRange = 0f..100f,
                enabled = !autoMode && isRunning
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("自动模式")
                    Spacer(modifier = Modifier.width(8.dp))
                    Switch(
                        checked = autoMode,
                        onCheckedChange = { autoMode = it }
                    )
                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = { isRunning = !isRunning },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (isRunning) Color.Red else Color.Green
                        )
                    ) {
                        Text(if (isRunning) "停止" else "启动")
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WaterPumpCard(
    deviceStatus: DeviceStatus?,
    onControlCommand: (CommandType) -> Unit
) {
    var isRunning by remember { mutableStateOf(deviceStatus?.pumpStatus ?: false) }
    var flowRate by remember { mutableStateOf(deviceStatus?.pumpFlow?.toFloat() ?: 25f) }
    var autoMode by remember { mutableStateOf(deviceStatus?.operationMode == OperationMode.AUTO) }
    var waterLevel by remember { mutableStateOf(68) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Filled.Water,
                        contentDescription = "水泵系统",
                        tint = if (isRunning) Color.Blue else Color.Gray
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        "水泵系统",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (isRunning) Icons.Filled.PlayArrow else Icons.Filled.Stop,
                        contentDescription = if (isRunning) "运行中" else "已停止",
                        tint = if (isRunning) Color.Blue else Color.Red,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        if (isRunning) "运行中" else "已停止",
                        fontSize = 12.sp,
                        color = if (isRunning) Color.Blue else Color.Red
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("流量: ${flowRate.toInt()} L/min")
                Text("水位: $waterLevel%", color = Color.Blue)
                Text("压力: 2.3 bar", color = Color.Green)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text("流量控制:", fontSize = 14.sp, fontWeight = FontWeight.Medium)
            Slider(
                value = flowRate,
                onValueChange = { flowRate = it },
                valueRange = 0f..50f,
                enabled = !autoMode && isRunning
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("自动模式")
                    Spacer(modifier = Modifier.width(8.dp))
                    Switch(
                        checked = autoMode,
                        onCheckedChange = { autoMode = it }
                    )
                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = { /* TODO: 设置时间表 */ }
                    ) {
                        Text("设置时间")
                    }
                    
                    Button(
                        onClick = { isRunning = !isRunning },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (isRunning) Color.Red else Color.Blue
                        )
                    ) {
                        Text(if (isRunning) "停止" else "启动")
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DryingSystemCard(
    deviceStatus: DeviceStatus?,
    onControlCommand: (CommandType) -> Unit
) {
    var isRunning by remember { mutableStateOf(deviceStatus?.dryerStatus ?: false) }
    var temperature by remember { mutableStateOf(45f) }
    var humidity by remember { mutableStateOf(35f) }
    var timerEnabled by remember { mutableStateOf(false) }
    var remainingTime by remember { mutableStateOf(120) } // 分钟
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Filled.LocalFireDepartment,
                        contentDescription = "干燥系统",
                        tint = if (isRunning) Color.Red else Color.Gray
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        "干燥系统",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (isRunning) Icons.Filled.PlayArrow else Icons.Filled.Stop,
                        contentDescription = if (isRunning) "运行中" else "已停止",
                        tint = if (isRunning) Color.Red else Color.Gray,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        if (isRunning) "运行中" else "已停止",
                        fontSize = 12.sp,
                        color = if (isRunning) Color.Red else Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("温度: ${temperature.toInt()}°C")
                Text("湿度: ${humidity.toInt()}%")
                if (timerEnabled) {
                    Text("剩余: ${remainingTime}分钟", color = Color.Blue)
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text("目标温度: ${temperature.toInt()}°C", fontSize = 14.sp)
            Slider(
                value = temperature,
                onValueChange = { temperature = it },
                valueRange = 20f..80f,
                enabled = isRunning
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text("目标湿度: ${humidity.toInt()}%", fontSize = 14.sp)
            Slider(
                value = humidity,
                onValueChange = { humidity = it },
                valueRange = 10f..60f,
                enabled = isRunning
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("定时功能")
                    Spacer(modifier = Modifier.width(8.dp))
                    Switch(
                        checked = timerEnabled,
                        onCheckedChange = { timerEnabled = it }
                    )
                }
                
                Button(
                    onClick = { isRunning = !isRunning },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isRunning) Color.Red else MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(if (isRunning) "停止" else "启动")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LightingSystemCard(
    deviceStatus: DeviceStatus?,
    onControlCommand: (CommandType) -> Unit
) {
    var isOn by remember { mutableStateOf(true) }
    var brightness by remember { mutableStateOf(80f) }
    var autoMode by remember { mutableStateOf(deviceStatus?.operationMode == OperationMode.AUTO) }
    var schedule by remember { mutableStateOf("06:00-22:00") }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Filled.Lightbulb,
                        contentDescription = "照明系统",
                        tint = if (isOn) Color.Yellow else Color.Gray
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        "照明系统",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (isOn) Icons.Filled.LightMode else Icons.Filled.DarkMode,
                        contentDescription = if (isOn) "开启" else "关闭",
                        tint = if (isOn) Color.Yellow else Color.Gray,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        if (isOn) "开启" else "关闭",
                        fontSize = 12.sp,
                        color = if (isOn) Color.Yellow else Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("亮度: ${brightness.toInt()}%")
                Text("功耗: ${(brightness * 0.8).toInt()}W")
                Text("计划: $schedule")
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text("亮度控制:", fontSize = 14.sp)
            Slider(
                value = brightness,
                onValueChange = { brightness = it },
                valueRange = 0f..100f,
                enabled = !autoMode && isOn
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("自动模式")
                    Spacer(modifier = Modifier.width(8.dp))
                    Switch(
                        checked = autoMode,
                        onCheckedChange = { autoMode = it }
                    )
                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = { /* TODO: 设置时间表 */ }
                    ) {
                        Text("设置时间")
                    }
                    
                    Button(
                        onClick = { isOn = !isOn },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (isOn) Color.Gray else Color.Yellow
                        )
                    ) {
                        Text(if (isOn) "关闭" else "开启")
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemperatureControlCard(
    deviceStatus: DeviceStatus?,
    onControlCommand: (CommandType) -> Unit
) {
    var isHeating by remember { mutableStateOf(false) }
    var isCooling by remember { mutableStateOf(true) }
    var targetTemp by remember { mutableStateOf(25f) }
    var currentTemp by remember { mutableStateOf(23.5f) }
    var autoMode by remember { mutableStateOf(deviceStatus?.operationMode == OperationMode.AUTO) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Filled.Thermostat,
                        contentDescription = "温度控制",
                        tint = when {
                            isHeating -> Color.Red
                            isCooling -> Color.Blue
                            else -> Color.Gray
                        }
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        "温度控制",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        when {
                            isHeating -> Icons.Filled.LocalFireDepartment
                            isCooling -> Icons.Filled.AcUnit
                            else -> Icons.Filled.Pause
                        },
                        contentDescription = when {
                            isHeating -> "加热中"
                            isCooling -> "制冷中"
                            else -> "待机"
                        },
                        tint = when {
                            isHeating -> Color.Red
                            isCooling -> Color.Blue
                            else -> Color.Gray
                        },
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        when {
                            isHeating -> "加热中"
                            isCooling -> "制冷中"
                            else -> "待机"
                        },
                        fontSize = 12.sp,
                        color = when {
                            isHeating -> Color.Red
                            isCooling -> Color.Blue
                            else -> Color.Gray
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("当前: ${currentTemp}°C")
                Text("目标: ${targetTemp.toInt()}°C")
                Text("差值: ${String.format("%.1f", targetTemp - currentTemp)}°C")
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text("目标温度: ${targetTemp.toInt()}°C", fontSize = 14.sp)
            Slider(
                value = targetTemp,
                onValueChange = { targetTemp = it },
                valueRange = 10f..40f,
                enabled = !autoMode
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text("自动模式")
                    Spacer(modifier = Modifier.width(8.dp))
                    Switch(
                        checked = autoMode,
                        onCheckedChange = { autoMode = it }
                    )
                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = { 
                            isHeating = true
                            isCooling = false
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (isHeating) Color.Red else Color.Gray
                        ),
                        enabled = !autoMode
                    ) {
                        Text("加热")
                    }
                    
                    Button(
                        onClick = { 
                            isHeating = false
                            isCooling = true
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (isCooling) Color.Blue else Color.Gray
                        ),
                        enabled = !autoMode
                    ) {
                        Text("制冷")
                    }
                    
                    OutlinedButton(
                        onClick = { 
                            isHeating = false
                            isCooling = false
                        },
                        enabled = !autoMode
                    ) {
                        Text("停止")
                    }
                }
            }
        }
    }
}

@Composable
fun PowerSystemScreen() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                "电力系统",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
        
        item { SolarPowerCard() }
        item { BatterySystemCard() }
        item { LoadManagementCard() }
        item { PowerStatisticsCard() }
        item { PowerSettingsCard() }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SolarPowerCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "太阳能发电",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Filled.WbSunny,
                        contentDescription = "太阳能",
                        tint = Color.Yellow,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        "在线",
                        fontSize = 12.sp,
                        color = Color.Green
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                PowerMetric("当前功率", "120W", Color.Green)
                PowerMetric("电压", "24.5V", Color.Blue)
                PowerMetric("电流", "4.9A", Orange)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text("今日发电", fontSize = 12.sp, color = Color.Gray)
                    Text("2.8 kWh", fontSize = 16.sp, fontWeight = FontWeight.Bold, color = Color.Green)
                }
                Column {
                    Text("本月发电", fontSize = 12.sp, color = Color.Gray)
                    Text("85.2 kWh", fontSize = 16.sp, fontWeight = FontWeight.Bold, color = Color.Blue)
                }
                Column {
                    Text("总发电量", fontSize = 12.sp, color = Color.Gray)
                    Text("1250 kWh", fontSize = 16.sp, fontWeight = FontWeight.Bold, color = Orange)
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 功率曲线
            Text("功率曲线", fontSize = 14.sp, fontWeight = FontWeight.Medium)
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Bottom
            ) {
                val powerData = listOf(0f, 20f, 60f, 120f, 100f, 40f, 0f)
                val hours = listOf("06:00", "08:00", "10:00", "12:00", "14:00", "16:00", "18:00")
                
                powerData.forEachIndexed { index, power ->
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier
                                .width(20.dp)
                                .height((power * 0.8).dp)
                                .background(
                                    color = Color.Yellow,
                                    shape = RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp)
                                )
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            power.toInt().toString(),
                            fontSize = 8.sp,
                            color = Color.Gray
                        )
                        if (index < hours.size) {
                            Text(
                                hours[index],
                                fontSize = 8.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BatterySystemCard() {
    var batteryLevel by remember { mutableStateOf(85f) }
    var isCharging by remember { mutableStateOf(true) }
    var temperature by remember { mutableStateOf(25.3f) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "电池系统",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (isCharging) Icons.Filled.BatteryChargingFull else Icons.Filled.BatteryFull,
                        contentDescription = if (isCharging) "充电中" else "放电中",
                        tint = if (isCharging) Color.Green else Color.Blue,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        if (isCharging) "充电中" else "放电中",
                        fontSize = 12.sp,
                        color = if (isCharging) Color.Green else Color.Blue
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text("电池电量", fontSize = 12.sp, color = Color.Gray)
                    Text("${batteryLevel.toInt()}%", fontSize = 24.sp, fontWeight = FontWeight.Bold, color = Color.Green)
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 电池电量条
                    Box(
                        modifier = Modifier
                            .width(100.dp)
                            .height(20.dp)
                            .background(
                                color = Color.Gray.copy(alpha = 0.3f),
                                shape = RoundedCornerShape(10.dp)
                            )
                    ) {
                        Box(
                            modifier = Modifier
                                .width(100.dp * (batteryLevel / 100f))
                                .height(20.dp)
                                .background(
                                    color = when {
                                        batteryLevel > 80 -> Color.Green
                                        batteryLevel > 50 -> Color.Yellow
                                        else -> Color.Red
                                    },
                                    shape = RoundedCornerShape(10.dp)
                                )
                        )
                    }
                }
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text("电池温度", fontSize = 12.sp, color = Color.Gray)
                    Text("${temperature}°C", fontSize = 20.sp, fontWeight = FontWeight.Bold, color = Color.Blue)
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        when {
                            temperature < 20 -> "偏低"
                            temperature > 35 -> "偏高"
                            else -> "正常"
                        },
                        fontSize = 12.sp,
                        color = when {
                            temperature < 20 -> Color.Blue
                            temperature > 35 -> Color.Red
                            else -> Color.Green
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text("电压", fontSize = 12.sp, color = Color.Gray)
                    Text("26.8V", fontSize = 14.sp, fontWeight = FontWeight.Bold)
                }
                Column {
                    Text("电流", fontSize = 12.sp, color = Color.Gray)
                    Text("3.2A", fontSize = 14.sp, fontWeight = FontWeight.Bold)
                }
                Column {
                    Text("功率", fontSize = 12.sp, color = Color.Gray)
                    Text("85.8W", fontSize = 14.sp, fontWeight = FontWeight.Bold)
                }
                Column {
                    Text("剩余时间", fontSize = 12.sp, color = Color.Gray)
                    Text("8.5h", fontSize = 14.sp, fontWeight = FontWeight.Bold)
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("充电状态", fontSize = 14.sp, fontWeight = FontWeight.Medium)
                Text(
                    if (isCharging) "太阳能充电" else "放电供电",
                    fontSize = 14.sp,
                    color = if (isCharging) Color.Green else Color.Blue
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoadManagementCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "负载管理",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("总负载功率", fontSize = 14.sp, color = Color.Gray)
                Text("95W", fontSize = 18.sp, fontWeight = FontWeight.Bold, color = Orange)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 负载列表
            LoadItem("通风系统", "25W", true, Color.Green)
            LoadItem("水泵系统", "30W", true, Color.Blue)
            LoadItem("照明系统", "20W", true, Color.Yellow)
            LoadItem("温控系统", "15W", false, Color.Gray)
            LoadItem("监控系统", "5W", true, Purple)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text("太阳能发电", fontSize = 12.sp, color = Color.Gray)
                    Text("120W", fontSize = 14.sp, fontWeight = FontWeight.Bold, color = Color.Green)
                }
                Column {
                    Text("电池供电", fontSize = 12.sp, color = Color.Gray)
                    Text("0W", fontSize = 14.sp, fontWeight = FontWeight.Bold, color = Color.Blue)
                }
                Column {
                    Text("负载功率", fontSize = 12.sp, color = Color.Gray)
                    Text("95W", fontSize = 14.sp, fontWeight = FontWeight.Bold, color = Orange)
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = 0.79f, // 95/120
                modifier = Modifier.fillMaxWidth(),
                color = Color.Green,
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("0W", fontSize = 12.sp, color = Color.Gray)
                Text("120W", fontSize = 12.sp, color = Color.Gray)
            }
        }
    }
}

@Composable
fun LoadItem(
    name: String,
    power: String,
    isActive: Boolean,
    color: Color
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                if (isActive) Icons.Filled.Power else Icons.Filled.PowerOff,
                contentDescription = if (isActive) "运行中" else "已停止",
                tint = if (isActive) color else Color.Gray,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                name,
                fontSize = 14.sp,
                color = if (isActive) Color.Black else Color.Gray
            )
        }
        
        Text(
            power,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = if (isActive) color else Color.Gray
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PowerStatisticsCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "发电统计",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticsItem("今日", "2.8 kWh", Color.Green)
                StatisticsItem("本周", "18.5 kWh", Color.Blue)
                StatisticsItem("本月", "85.2 kWh", Orange)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticsItem("峰值功率", "150W", Color.Red)
                StatisticsItem("平均功率", "95W", Color.Green)
                StatisticsItem("效率", "85%", Color.Blue)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("发电效率", fontSize = 14.sp, fontWeight = FontWeight.Medium)
                Text("85%", fontSize = 16.sp, fontWeight = FontWeight.Bold, color = Color.Green)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = 0.85f,
                modifier = Modifier.fillMaxWidth(),
                color = Color.Green,
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { /* TODO: 查看详细统计 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("详细统计")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 导出数据 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("导出数据")
                }
            }
        }
    }
}

@Composable
fun StatisticsItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        Text(
            value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PowerSettingsCard() {
    var autoMode by remember { mutableStateOf(true) }
    var batteryThreshold by remember { mutableStateOf("20") }
    var maxLoadPower by remember { mutableStateOf("100") }
    var emergencyMode by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "电力设置",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Settings, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("自动模式")
                }
                Switch(
                    checked = autoMode,
                    onCheckedChange = { autoMode = it }
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text("电池阈值 (%)", fontSize = 14.sp, fontWeight = FontWeight.Medium)
            OutlinedTextField(
                value = batteryThreshold,
                onValueChange = { batteryThreshold = it },
                label = { Text("低电量阈值") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text("最大负载功率 (W)", fontSize = 14.sp, fontWeight = FontWeight.Medium)
            OutlinedTextField(
                value = maxLoadPower,
                onValueChange = { maxLoadPower = it },
                label = { Text("最大负载功率") },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Warning, contentDescription = null, tint = Color.Red)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("紧急模式")
                }
                Switch(
                    checked = emergencyMode,
                    onCheckedChange = { emergencyMode = it }
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { /* TODO: 保存设置 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("保存设置")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 恢复默认 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("恢复默认")
                }
            }
        }
    }
}

@Composable
fun AlertsScreen() {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                "报警管理",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
        
        item { AlertOverviewCard() }
        item { ActiveAlertsCard() }
        item { AlertHistoryCard() }
        item { AlertSettingsCard() }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlertOverviewCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "报警概览",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                AlertMetric("紧急", 1, Color.Red)
                AlertMetric("警告", 3, Color.Yellow)
                AlertMetric("信息", 5, Color.Blue)
                AlertMetric("已处理", 12, Color.Green)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { /* TODO: 全部确认 */ },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(containerColor = Color.Green)
                ) {
                    Text("全部确认")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 清除历史 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("清除历史")
                }
            }
        }
    }
}

@Composable
fun AlertMetric(
    label: String,
    count: Int,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            count.toString(),
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            label,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActiveAlertsCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "活动报警",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 模拟活动报警列表
            val activeAlerts = listOf(
                AlertItem(
                    "温度异常",
                    "温度过高: 32.5°C",
                    "2分钟前",
                    AlertLevel.EMERGENCY,
                    Icons.Filled.Thermostat
                ),
                AlertItem(
                    "湿度异常",
                    "湿度过高: 85%",
                    "5分钟前",
                    AlertLevel.WARNING,
                    Icons.Filled.Water
                ),
                AlertItem(
                    "电池电量低",
                    "电池电量: 15%",
                    "10分钟前",
                    AlertLevel.WARNING,
                    Icons.Filled.BatteryAlert
                ),
                AlertItem(
                    "设备离线",
                    "水泵系统离线",
                    "15分钟前",
                    AlertLevel.INFO,
                    Icons.Filled.Warning
                )
            )
            
            activeAlerts.forEach { alert ->
                AlertListItem(alert)
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlertListItem(alert: AlertItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (alert.level) {
                AlertLevel.EMERGENCY -> Color.Red.copy(alpha = 0.1f)
                AlertLevel.CRITICAL -> Color.Red.copy(alpha = 0.15f)
                AlertLevel.WARNING -> Color.Yellow.copy(alpha = 0.1f)
                AlertLevel.INFO -> Color.Blue.copy(alpha = 0.1f)
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    alert.icon,
                    contentDescription = alert.title,
                    tint = when (alert.level) {
                        AlertLevel.EMERGENCY -> Color.Red
                        AlertLevel.CRITICAL -> Color.Red
                        AlertLevel.WARNING -> Color.Yellow
                        AlertLevel.INFO -> Color.Blue
                    },
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column {
                    Text(
                        alert.title,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        alert.description,
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        alert.time,
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                OutlinedButton(
                    onClick = { /* TODO: 确认报警 */ },
                    modifier = Modifier.height(32.dp)
                ) {
                    Text("确认", fontSize = 12.sp)
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 忽略报警 */ },
                    modifier = Modifier.height(32.dp)
                ) {
                    Text("忽略", fontSize = 12.sp)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlertHistoryCard() {
    var selectedPeriod by remember { mutableStateOf("今日") }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "报警历史",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = { selectedPeriod = "今日" },
                        modifier = Modifier.height(32.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            containerColor = if (selectedPeriod == "今日") MaterialTheme.colorScheme.primaryContainer else Color.Transparent
                        )
                    ) {
                        Text("今日", fontSize = 12.sp)
                    }
                    OutlinedButton(
                        onClick = { selectedPeriod = "本周" },
                        modifier = Modifier.height(32.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            containerColor = if (selectedPeriod == "本周") MaterialTheme.colorScheme.primaryContainer else Color.Transparent
                        )
                    ) {
                        Text("本周", fontSize = 12.sp)
                    }
                    OutlinedButton(
                        onClick = { selectedPeriod = "本月" },
                        modifier = Modifier.height(32.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            containerColor = if (selectedPeriod == "本月") MaterialTheme.colorScheme.primaryContainer else Color.Transparent
                        )
                    ) {
                        Text("本月", fontSize = 12.sp)
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 模拟历史报警列表
            val historyAlerts = listOf(
                AlertItem(
                    "温度异常",
                    "温度过高: 31.2°C",
                    "1小时前",
                    AlertLevel.WARNING,
                    Icons.Filled.Thermostat
                ),
                AlertItem(
                    "设备维护",
                    "通风系统需要维护",
                    "3小时前",
                    AlertLevel.INFO,
                    Icons.Filled.Build
                ),
                AlertItem(
                    "电池电量低",
                    "电池电量: 12%",
                    "5小时前",
                    AlertLevel.WARNING,
                    Icons.Filled.BatteryAlert
                ),
                AlertItem(
                    "网络断开",
                    "PLC连接断开",
                    "1天前",
                    AlertLevel.EMERGENCY,
                    Icons.Filled.WifiOff
                )
            )
            
            historyAlerts.forEach { alert ->
                AlertHistoryItem(alert)
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlertHistoryItem(alert: AlertItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Gray.copy(alpha = 0.05f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    alert.icon,
                    contentDescription = alert.title,
                    tint = Color.Gray,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column {
                    Text(
                        alert.title,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        alert.description,
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        alert.time,
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
            
            Icon(
                Icons.Filled.CheckCircle,
                contentDescription = "已处理",
                tint = Color.Green,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlertSettingsCard() {
    var enableAlerts by remember { mutableStateOf(true) }
    var soundEnabled by remember { mutableStateOf(true) }
    var vibrationEnabled by remember { mutableStateOf(false) }
    var autoAcknowledge by remember { mutableStateOf(false) }
    var emergencyOnly by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "报警设置",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Notifications, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("启用报警")
                }
                Switch(
                    checked = enableAlerts,
                    onCheckedChange = { enableAlerts = it }
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.VolumeUp, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("声音提醒")
                }
                Switch(
                    checked = soundEnabled,
                    onCheckedChange = { soundEnabled = it },
                    enabled = enableAlerts
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Vibration, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("震动提醒")
                }
                Switch(
                    checked = vibrationEnabled,
                    onCheckedChange = { vibrationEnabled = it },
                    enabled = enableAlerts
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.AutoMode, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("自动确认")
                }
                Switch(
                    checked = autoAcknowledge,
                    onCheckedChange = { autoAcknowledge = it },
                    enabled = enableAlerts
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.PriorityHigh, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("仅紧急报警")
                }
                Switch(
                    checked = emergencyOnly,
                    onCheckedChange = { emergencyOnly = it },
                    enabled = enableAlerts
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { /* TODO: 保存设置 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("保存设置")
                }
                
                OutlinedButton(
                    onClick = { /* TODO: 测试报警 */ },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("测试报警")
                }
            }
        }
    }
}

// 数据类
data class AlertItem(
    val title: String,
    val description: String,
    val time: String,
    val level: AlertLevel,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
)

@Composable
fun SettingsScreen() {
    val settingsViewModel: SettingsViewModel = hiltViewModel()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                "系统设置",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }

        item { PLCConnectionCard(settingsViewModel) }
        item { MQTTSettingsCard(settingsViewModel) }
        item { NotificationSettingsCard() }
        item { SystemSettingsCard() }
        item { AboutCard() }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PLCConnectionCard(settingsViewModel: SettingsViewModel) {
    var ipAddress by remember { mutableStateOf("*************") }
    var port by remember { mutableStateOf("502") }
    var deviceId by remember { mutableStateOf("001") }

    val uiState by settingsViewModel.uiState.collectAsState()
    val networkSettings by settingsViewModel.networkSettings.collectAsState()

    // 从网络设置中提取服务器地址
    LaunchedEffect(networkSettings) {
        val serverUrl = networkSettings.serverUrl
        if (serverUrl.startsWith("http://")) {
            val addressPart = serverUrl.removePrefix("http://")
            val parts = addressPart.split(":")
            if (parts.isNotEmpty()) {
                ipAddress = parts[0]
                if (parts.size > 1) {
                    port = parts[1]
                }
            }
        }
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        "PLC智慧渔箱连接",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        "将进行真实网络连接测试",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (uiState.isTestingPLC) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            "测试中...",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.primary
                        )
                    } else {
                        val isConnected = settingsViewModel.getPlcConnectionStatus()
                        Icon(
                            if (isConnected) Icons.Filled.CheckCircle else Icons.Filled.Cancel,
                            contentDescription = if (isConnected) "已连接" else "未连接",
                            tint = if (isConnected) Color.Green else Color.Red,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            if (isConnected) "已连接" else "未连接",
                            fontSize = 12.sp,
                            color = if (isConnected) Color.Green else Color.Red
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            OutlinedTextField(
                value = ipAddress,
                onValueChange = { ipAddress = it },
                label = { Text("IP地址") },
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = { Icon(Icons.Filled.Computer, contentDescription = null) }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = port,
                    onValueChange = { port = it },
                    label = { Text("端口") },
                    modifier = Modifier.weight(1f),
                    leadingIcon = { Icon(Icons.Filled.Router, contentDescription = null) }
                )
                
                OutlinedTextField(
                    value = deviceId,
                    onValueChange = { deviceId = it },
                    label = { Text("设备ID") },
                    modifier = Modifier.weight(1f),
                    leadingIcon = { Icon(Icons.Filled.Tag, contentDescription = null) }
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 显示PLC状态消息
            uiState.plcMessage?.let { message ->
                Text(
                    text = message,
                    color = MaterialTheme.colorScheme.primary,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }

            uiState.plcError?.let { error ->
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.error,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val isConnected = settingsViewModel.getPlcConnectionStatus()

                Button(
                    onClick = {
                        if (isConnected) {
                            settingsViewModel.disconnectPlc()
                        } else {
                            settingsViewModel.connectPlc(ipAddress, port, deviceId)
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isTestingPLC,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isConnected) Color.Red else MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(if (isConnected) "断开连接" else "连接")
                }

                OutlinedButton(
                    onClick = {
                        settingsViewModel.testPlcConnection(ipAddress, port)
                    },
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isTestingPLC
                ) {
                    Text("测试连接")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MQTTSettingsCard(settingsViewModel: SettingsViewModel) {
    val uiState by settingsViewModel.uiState.collectAsState()
    val networkSettings by settingsViewModel.networkSettings.collectAsState()

    var brokerUrl by remember { mutableStateOf("tcp://*************:1883") }
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var clientId by remember { mutableStateOf("FishFarmApp") }

    // 从网络设置中同步数据
    LaunchedEffect(networkSettings) {
        brokerUrl = networkSettings.mqttBrokerUrl
        username = networkSettings.mqttUsername
        password = networkSettings.mqttPassword
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    "MQTT服务器设置",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (uiState.isTestingMQTT) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            "测试中...",
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.primary
                        )
                    } else {
                        val isConnected = settingsViewModel.getMqttConnectionStatus()
                        Icon(
                            if (isConnected) Icons.Filled.CloudDone else Icons.Filled.CloudOff,
                            contentDescription = if (isConnected) "已连接" else "未连接",
                            tint = if (isConnected) Color.Green else Color.Gray,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            if (isConnected) "已连接" else "未连接",
                            fontSize = 12.sp,
                            color = if (isConnected) Color.Green else Color.Gray
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            OutlinedTextField(
                value = brokerUrl,
                onValueChange = { brokerUrl = it },
                label = { Text("服务器地址") },
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = { Icon(Icons.Filled.Cloud, contentDescription = null) }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            OutlinedTextField(
                value = clientId,
                onValueChange = { clientId = it },
                label = { Text("客户端ID") },
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = { Icon(Icons.Filled.Smartphone, contentDescription = null) }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = username,
                    onValueChange = { username = it },
                    label = { Text("用户名") },
                    modifier = Modifier.weight(1f),
                    leadingIcon = { Icon(Icons.Filled.Person, contentDescription = null) }
                )
                
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = { Text("密码") },
                    modifier = Modifier.weight(1f),
                    leadingIcon = { Icon(Icons.Filled.Lock, contentDescription = null) },
                    visualTransformation = PasswordVisualTransformation()
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 显示MQTT状态消息
            uiState.mqttMessage?.let { message ->
                Text(
                    text = message,
                    color = MaterialTheme.colorScheme.primary,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }

            uiState.mqttError?.let { error ->
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.error,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                val isConnected = settingsViewModel.getMqttConnectionStatus()

                Button(
                    onClick = {
                        if (isConnected) {
                            settingsViewModel.disconnectMqtt()
                        } else {
                            // 先保存当前设置，然后连接
                            val newSettings = networkSettings.copy(
                                mqttBrokerUrl = brokerUrl,
                                mqttUsername = username,
                                mqttPassword = password
                            )
                            settingsViewModel.updateNetworkSettings(newSettings)
                            settingsViewModel.connectMqtt(
                                username.takeIf { it.isNotBlank() },
                                password.takeIf { it.isNotBlank() }
                            )
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isTestingMQTT,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isConnected) Color.Red else MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text(if (isConnected) "断开" else "连接")
                }

                OutlinedButton(
                    onClick = {
                        settingsViewModel.testMqttConnection(
                            brokerUrl,
                            username.takeIf { it.isNotBlank() },
                            password.takeIf { it.isNotBlank() }
                        )
                    },
                    modifier = Modifier.weight(1f),
                    enabled = !uiState.isTestingMQTT
                ) {
                    Text("测试连接")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsCard() {
    var enableNotifications by remember { mutableStateOf(true) }
    var enableSoundAlerts by remember { mutableStateOf(true) }
    var enableVibration by remember { mutableStateOf(false) }
    var temperatureAlert by remember { mutableStateOf(true) }
    var humidityAlert by remember { mutableStateOf(true) }
    var powerAlert by remember { mutableStateOf(true) }
    var deviceAlert by remember { mutableStateOf(true) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "通知与报警设置",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 通知开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Notifications, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("启用通知")
                }
                Switch(
                    checked = enableNotifications,
                    onCheckedChange = { enableNotifications = it }
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 声音提醒
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.VolumeUp, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("声音提醒")
                }
                Switch(
                    checked = enableSoundAlerts,
                    onCheckedChange = { enableSoundAlerts = it },
                    enabled = enableNotifications
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 震动提醒
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Vibration, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("震动提醒")
                }
                Switch(
                    checked = enableVibration,
                    onCheckedChange = { enableVibration = it },
                    enabled = enableNotifications
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                "报警类型",
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            // 温度报警
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Thermostat, contentDescription = null, tint = Color.Red)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("温度异常报警")
                }
                Switch(
                    checked = temperatureAlert,
                    onCheckedChange = { temperatureAlert = it },
                    enabled = enableNotifications
                )
            }
            
            // 湿度报警
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Water, contentDescription = null, tint = Color.Blue)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("湿度异常报警")
                }
                Switch(
                    checked = humidityAlert,
                    onCheckedChange = { humidityAlert = it },
                    enabled = enableNotifications
                )
            }
            
            // 电力系统报警
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.ElectricBolt, contentDescription = null, tint = Color.Yellow)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("电力系统报警")
                }
                Switch(
                    checked = powerAlert,
                    onCheckedChange = { powerAlert = it },
                    enabled = enableNotifications
                )
            }
            
            // 设备故障报警
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Build, contentDescription = null, tint = Orange)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("设备故障报警")
                }
                Switch(
                    checked = deviceAlert,
                    onCheckedChange = { deviceAlert = it },
                    enabled = enableNotifications
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SystemSettingsCard() {
    var autoSync by remember { mutableStateOf(true) }
    var syncInterval by remember { mutableStateOf("5分钟") }
    var dataRetention by remember { mutableStateOf("30天") }
    var enableDarkMode by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "系统设置",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 自动同步
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Sync, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("自动同步数据")
                }
                Switch(
                    checked = autoSync,
                    onCheckedChange = { autoSync = it }
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 同步间隔
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Schedule, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("同步间隔")
                }
                Text(
                    syncInterval,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 数据保留时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.Storage, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("数据保留")
                }
                Text(
                    dataRetention,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 深色模式
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(Icons.Filled.DarkMode, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("深色模式")
                }
                Switch(
                    checked = enableDarkMode,
                    onCheckedChange = { enableDarkMode = it }
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { 
                        // TODO: 实现数据清理逻辑
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("清理缓存")
                }
                
                OutlinedButton(
                    onClick = { 
                        // TODO: 实现数据导出逻辑
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("导出数据")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "关于应用",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("应用版本")
                Text("v1.0.0", color = MaterialTheme.colorScheme.primary)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("构建版本")
                Text("20241230", color = MaterialTheme.colorScheme.primary)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("开发者")
                Text("智慧渔场团队", color = MaterialTheme.colorScheme.primary)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = { 
                    // TODO: 实现检查更新逻辑
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("检查更新")
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun DashboardPreview() {
    MyApplicationTheme {
        DashboardScreen()
    }
}