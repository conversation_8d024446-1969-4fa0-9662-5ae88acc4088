package com.ys.myapplication.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ys.myapplication.data.model.*
import com.ys.myapplication.data.repository.DeviceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import javax.inject.Inject

/**
 * 设置界面ViewModel
 * 管理系统设置、用户偏好和配置
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val deviceRepository: DeviceRepository
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    // 系统设置
    private val _systemSettings = MutableStateFlow(SystemSettings())
    val systemSettings: StateFlow<SystemSettings> = _systemSettings.asStateFlow()
    
    // 通知设置
    private val _notificationSettings = MutableStateFlow(NotificationSettings())
    val notificationSettings: StateFlow<NotificationSettings> = _notificationSettings.asStateFlow()
    
    // 网络设置
    private val _networkSettings = MutableStateFlow(NetworkSettings())
    val networkSettings: StateFlow<NetworkSettings> = _networkSettings.asStateFlow()
    
    // 用户信息
    private val _userInfo = MutableStateFlow(UserInfo())
    val userInfo: StateFlow<UserInfo> = _userInfo.asStateFlow()
    
    init {
        loadSettings()
    }
    
    /**
     * 加载设置
     */
    private fun loadSettings() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // 这里应该从SharedPreferences或数据库加载设置
                // 暂时使用默认值
                
                _uiState.value = _uiState.value.copy(isLoading = false)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 更新系统设置
     */
    fun updateSystemSettings(settings: SystemSettings) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isSaving = true)
                
                // 这里应该保存到SharedPreferences或数据库
                _systemSettings.value = settings
                
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    message = "系统设置已保存"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 更新通知设置
     */
    fun updateNotificationSettings(settings: NotificationSettings) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isSaving = true)
                
                _notificationSettings.value = settings
                
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    message = "通知设置已保存"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 更新网络设置
     */
    fun updateNetworkSettings(settings: NetworkSettings) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isSaving = true)
                
                _networkSettings.value = settings
                
                // 重新初始化网络连接
                deviceRepository.updateNetworkSettings(settings)
                
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    message = "网络设置已保存"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 更新用户信息
     */
    fun updateUserInfo(userInfo: UserInfo) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isSaving = true)
                
                _userInfo.value = userInfo
                
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    message = "用户信息已保存"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSaving = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 测试网络连接
     */
    fun testNetworkConnection() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isTesting = true)

                val isConnected = deviceRepository.testConnection()

                _uiState.value = _uiState.value.copy(
                    isTesting = false,
                    message = if (isConnected) "网络连接正常" else "网络连接失败"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isTesting = false,
                    error = "网络测试失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 测试MQTT连接
     */
    fun testMqttConnection(brokerUrl: String, username: String? = null, password: String? = null) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isTestingMQTT = true, mqttError = null, mqttMessage = null)

                val isConnected = (deviceRepository as? com.ys.myapplication.data.repository.DeviceRepositoryImpl)
                    ?.testMqttConnection(brokerUrl, username, password) ?: false

                _uiState.value = _uiState.value.copy(
                    isTestingMQTT = false,
                    mqttMessage = if (isConnected) "MQTT连接测试成功" else "MQTT连接测试失败"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isTestingMQTT = false,
                    mqttError = "MQTT连接测试失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 测试PLC连接
     */
    fun testPlcConnection(ipAddress: String, port: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isTestingPLC = true, plcError = null, plcMessage = null)

                // 进行真正的网络连接测试
                val isConnected = withContext(Dispatchers.IO) {
                    testNetworkConnection(ipAddress, port)
                }

                _uiState.value = _uiState.value.copy(
                    isTestingPLC = false,
                    plcMessage = if (isConnected) "PLC连接测试成功" else "PLC连接测试失败：无法连接到指定地址"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isTestingPLC = false,
                    plcError = "PLC连接测试失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 测试网络连接
     */
    private suspend fun testNetworkConnection(ipAddress: String, port: String): Boolean {
        return try {
            // 首先验证IP地址格式
            val isValidIp = ipAddress.matches(Regex("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$"))
            if (!isValidIp) {
                return false
            }

            // 验证端口范围
            val portNumber = port.toIntOrNull()
            if (portNumber == null || portNumber !in 1..65535) {
                return false
            }

            // 进行真正的Socket连接测试
            withTimeout(10000) { // 10秒超时
                val socket = java.net.Socket()
                try {
                    socket.connect(java.net.InetSocketAddress(ipAddress, portNumber), 5000) // 5秒连接超时
                    socket.close()
                    true
                } catch (e: Exception) {
                    try { socket.close() } catch (ignored: Exception) { }
                    false
                }
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 连接MQTT服务器
     */
    fun connectMqtt(username: String? = null, password: String? = null) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isTestingMQTT = true, mqttError = null, mqttMessage = null)

                (deviceRepository as? com.ys.myapplication.data.repository.DeviceRepositoryImpl)
                    ?.connectMqtt(username, password)

                // 等待一下让连接建立
                delay(2000)

                val isConnected = (deviceRepository as? com.ys.myapplication.data.repository.DeviceRepositoryImpl)
                    ?.getMqttConnectionStatus() ?: false

                _uiState.value = _uiState.value.copy(
                    isTestingMQTT = false,
                    mqttMessage = if (isConnected) "MQTT连接成功" else "MQTT连接失败"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isTestingMQTT = false,
                    mqttError = "MQTT连接失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 连接PLC设备
     */
    fun connectPlc(ipAddress: String, port: String, deviceId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isTestingPLC = true, plcError = null, plcMessage = null)

                // 进行真正的网络连接测试
                val isConnected = withContext(Dispatchers.IO) {
                    testNetworkConnection(ipAddress, port)
                }

                _uiState.value = _uiState.value.copy(
                    isTestingPLC = false,
                    plcConnected = isConnected,
                    plcMessage = if (isConnected) "PLC连接成功" else "PLC连接失败：无法连接到指定地址"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isTestingPLC = false,
                    plcConnected = false,
                    plcError = "PLC连接失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 断开MQTT连接
     */
    fun disconnectMqtt() {
        viewModelScope.launch {
            try {
                (deviceRepository as? com.ys.myapplication.data.repository.DeviceRepositoryImpl)
                    ?.disconnectMqtt()

                _uiState.value = _uiState.value.copy(
                    mqttMessage = "MQTT连接已断开"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    mqttError = "断开MQTT连接失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 断开PLC连接
     */
    fun disconnectPlc() {
        viewModelScope.launch {
            try {
                // 模拟断开PLC连接
                _uiState.value = _uiState.value.copy(
                    plcConnected = false,
                    plcMessage = "PLC连接已断开"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    plcError = "断开PLC连接失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取MQTT连接状态
     */
    fun getMqttConnectionStatus(): Boolean {
        return (deviceRepository as? com.ys.myapplication.data.repository.DeviceRepositoryImpl)
            ?.getMqttConnectionStatus() ?: false
    }

    /**
     * 获取PLC连接状态
     */
    fun getPlcConnectionStatus(): Boolean {
        return _uiState.value.plcConnected
    }
    
    /**
     * 导出数据
     */
    fun exportData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isExporting = true)
                
                // 这里应该实现数据导出逻辑
                // deviceRepository.exportData()
                
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    message = "数据导出完成"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isExporting = false,
                    error = "数据导出失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 导入数据
     */
    fun importData(filePath: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isImporting = true)
                
                // 这里应该实现数据导入逻辑
                // deviceRepository.importData(filePath)
                
                _uiState.value = _uiState.value.copy(
                    isImporting = false,
                    message = "数据导入完成"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isImporting = false,
                    error = "数据导入失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        viewModelScope.launch {
            try {
                // 这里应该实现缓存清除逻辑
                // deviceRepository.clearCache()
                
                _uiState.value = _uiState.value.copy(
                    message = "缓存已清除"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "清除缓存失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 重置设置
     */
    fun resetSettings() {
        viewModelScope.launch {
            try {
                _systemSettings.value = SystemSettings()
                _notificationSettings.value = NotificationSettings()
                _networkSettings.value = NetworkSettings()
                
                _uiState.value = _uiState.value.copy(
                    message = "设置已重置为默认值"
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "重置设置失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
    
    /**
     * 清除错误
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * 设置界面UI状态
 */
data class SettingsUiState(
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isTesting: Boolean = false,
    val isTestingPLC: Boolean = false,
    val isTestingMQTT: Boolean = false,
    val isExporting: Boolean = false,
    val isImporting: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val plcMessage: String? = null,
    val plcError: String? = null,
    val mqttMessage: String? = null,
    val mqttError: String? = null,
    val plcConnected: Boolean = false
)

/**
 * 系统设置
 */
data class SystemSettings(
    val language: String = "zh-CN",
    val theme: String = "auto", // auto, light, dark
    val autoRefreshInterval: Int = 30, // 秒
    val dataRetentionDays: Int = 30,
    val enableDebugMode: Boolean = false
)

/**
 * 通知设置
 */
data class NotificationSettings(
    val enableNotifications: Boolean = true,
    val enableCriticalAlerts: Boolean = true,
    val enableHighAlerts: Boolean = true,
    val enableMediumAlerts: Boolean = false,
    val enableLowAlerts: Boolean = false,
    val soundEnabled: Boolean = true,
    val vibrationEnabled: Boolean = true,
    val quietHoursEnabled: Boolean = false,
    val quietHoursStart: String = "22:00",
    val quietHoursEnd: String = "08:00"
)

/**
 * 网络设置
 */
data class NetworkSettings(
    val serverUrl: String = "http://192.168.1.100:8080",
    val mqttBrokerUrl: String = "tcp://192.168.1.100:1883",
    val mqttUsername: String = "",
    val mqttPassword: String = "",
    val connectionTimeout: Int = 30,
    val keepAliveInterval: Int = 60,
    val enableSSL: Boolean = false
)

/**
 * 用户信息
 */
data class UserInfo(
    val username: String = "",
    val email: String = "",
    val phone: String = "",
    val company: String = "",
    val role: String = "操作员"
)
